package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditorManager
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.psi.MemberInfo
import com.taobao.mc.aimi.psi.MemberKind
import com.taobao.mc.aimi.psi.ObjectTypeResolver
import com.taobao.mc.aimi.util.mSelectedTextEditor
import kotlin.time.measureTime

class SmartCompletionAction : AnAction("索引测试") {
    private val logger = LoggerManager.getLogger(javaClass)

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val time = measureTime {
            val smartCompletionService = project.service<ObjectTypeResolver>()
            val editorManager = FileEditorManager.getInstance(project)
            val textEditor = editorManager.mSelectedTextEditor ?: return

            smartCompletionService.resolveObjectAtCursor(textEditor)
                // .distinctBy { "${it.type}:${it.qualifiedType}" }
                .forEachIndexed { index, targetObject ->
                    logger.info("=== Smart Completion Result ${index + 1} ===")
                    logger.info("Name: ${targetObject.name}")
                    logger.info("Type: ${targetObject.type}")
                    logger.info("Qualified Type: ${targetObject.qualifiedType ?: "N/A"}")
                    logger.info("Kind: ${targetObject.kind}")
                    logger.info("Nullable: ${targetObject.isNullable}")
                    logger.info("Confidence: ${targetObject.confidence}")
                    logger.info("Members count: ${targetObject.members.size}")
                    logger.info("Filepath: ${targetObject.filepath}")
                    if (!targetObject.content.isNullOrEmpty()) {
                        targetObject.content.lines().forEach {
                            logger.info("Content: $it")
                        }
                    }
                    if (targetObject.members.isNotEmpty()) {
                        targetObject.members.forEach { member ->
                            val prefix = "${member.visibility}${renderStatic(member)}"
                            val desc = when (member.kind) {
                                MemberKind.FIELD -> {
                                    "$prefix ${member.name}: ${member.type}"
                                }

                                MemberKind.PROPERTY -> {
                                    "$prefix ${member.name}: ${member.type}"
                                }

                                MemberKind.METHOD -> {
                                    "$prefix ${member.signature}"
                                }

                                MemberKind.CONSTRUCTOR -> {
                                    "$prefix ${member.signature}"
                                }
                            }
                            logger.info("  - ${member.kind}: $desc")
                        }
                    }
                    logger.info("==============================")
                }
        }
        logger.info("smart completion time: $time ms")
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }

    private fun renderStatic(memberInfo: MemberInfo) = if (memberInfo.isStatic) " static" else ""
}