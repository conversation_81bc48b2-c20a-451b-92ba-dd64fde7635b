package com.taobao.mc.aimi.ext.error

// class AIMIErrorSubmitter : ErrorReportSubmitter() {
//
//     init {
//         // Sentry.init { config ->
//         //     config.dsn = SENTRY_DSN
//         //     config.isSendDefaultPii = false
//         //     config.setTag("ide_version", ApplicationInfo.getInstance().build.asString())
//         //     config.setTag("jcef_supported", JBCefApp.isSupported().toString())
//         // }
//     }
//
//     override fun getReportActionText() =
//         "Report to Continue"
//
//     override fun submit(
//         events: Array<out IdeaLoggingEvent?>,
//         additionalInfo: String?,
//         parentComponent: Component,
//         consumer: Consumer<in SubmittedReportInfo>
//     ): Boolean {
//         try {
//             // val event = events.filterIsInstance<IdeaReportingEvent>().firstOrNull() ?: return false
//             // val sentryEvent = SentryEvent()
//             // sentryEvent.throwable = event.data.throwable
//             // sentryEvent.message = Message().apply { message = additionalInfo }
//             // sentryEvent.setTag("plugin_version", event.plugin?.version)
//             // Sentry.captureEvent(sentryEvent)
//         } catch (_: Exception) {
//             consumer.consume(SubmittedReportInfo(SubmissionStatus.FAILED))
//             return false
//         }
//         consumer.consume(SubmittedReportInfo(SubmissionStatus.NEW_ISSUE))
//         return true
//     }
//
// }
